import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { UserCallService, ICallState, IUserCallData } from '../../../core/services/user-call-services/user-call.service';
import { JitsiIntegService } from '../../../core/services/jitsi-integ-services/jitsi-integ.service';
import { IStartCallSessionResponse } from '../../../core/interfaces/calls/istart-call-session-response';

@Component({
  selector: 'app-incoming-call',
  templateUrl: './incoming-call.component.html',
  styleUrls: ['./incoming-call.component.scss']
})
export class IncomingCallComponent implements OnInit, OnDestroy {
  callState: ICallState | null = null;
  private callStateSubscription: Subscription | null = null;

  constructor(
    private userCallService: UserCallService,
    private jitsiService: JitsiIntegService
  ) { }

  ngOnInit(): void {
    this.callStateSubscription = this.userCallService.callState$.subscribe(state => {
      this.callState = state;
    });
  }

  ngOnDestroy(): void {
    if (this.callStateSubscription) {
      this.callStateSubscription.unsubscribe();
    }
  }

  acceptCall(): void {
    if (this.callState?.currentCall) {
      this.userCallService.acceptCall(this.callState.currentCall);
      // Start Jitsi call
      this.startJitsiCall(this.callState.currentCall);
    }
  }

  rejectCall(): void {
    if (this.callState?.currentCall) {
      this.userCallService.rejectCall(this.callState.currentCall);
    }
  }

  endCall(): void {
    this.userCallService.endCall();
    // End Jitsi call if active
    if (this.jitsiService.api) {
      this.jitsiService.api.dispose();
    }
  }

  private startJitsiCall(callData: IUserCallData): void {
    // Create a mock response for Jitsi integration
    const mockJitsiResponse: IStartCallSessionResponse = {
      callId: callData.callId,
      roomName: `room_${callData.callId}`,
      token: '', // In a real implementation, you'd get this from your backend
      url: '' // Required by the interface
    };

    // Start Jitsi call
    this.jitsiService.joinCall(mockJitsiResponse, false);
  }
}
