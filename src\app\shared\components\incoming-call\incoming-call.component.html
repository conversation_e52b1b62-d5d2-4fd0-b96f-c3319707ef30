<!-- Incoming Call Modal -->
<div class="incoming-call-overlay" *ngIf="callState?.isReceivingCall && callState?.currentCall">
  <div class="incoming-call-modal">
    <div class="caller-info">
      <div class="caller-avatar">
        <img [src]="(callState.currentCall?.callerAvatar) || 'assets/images/default-avatar.png'"
             alt="Caller Avatar"
             class="avatar-img">
      </div>
      <h3 class="caller-name">{{ callState.currentCall?.callerName || 'Unknown Caller' }}</h3>
      <p class="call-status">{{ 'INCOMING_CALL' | translate }}</p>
    </div>

    <div class="call-actions">
      <button class="btn-reject" (click)="rejectCall()">
        <i class="fas fa-phone-slash"></i>
        <span>{{ 'REJECT' | translate }}</span>
      </button>

      <button class="btn-accept" (click)="acceptCall()">
        <i class="fas fa-phone"></i>
        <span>{{ 'ACCEPT' | translate }}</span>
      </button>
    </div>
  </div>
</div>

<!-- Call Status Display -->
<div class="call-status-display" *ngIf="callState?.isCalling">
  <div class="calling-modal">
    <div class="calling-info">
      <div class="calling-avatar">
        <img [src]="(callState.currentCall?.callerAvatar) || 'assets/images/default-avatar.png'"
             alt="Calling Avatar"
             class="avatar-img">
      </div>
      <h3 class="calling-name">{{ 'CALLING' | translate }}...</h3>
      <p class="call-status">{{ (callState?.callStatus || 'calling') | uppercase }}</p>
    </div>

    <div class="call-actions">
      <button class="btn-end-call" (click)="endCall()">
        <i class="fas fa-phone-slash"></i>
        <span>{{ 'END_CALL' | translate }}</span>
      </button>
    </div>
  </div>
</div>

<!-- Active Call Display -->
<div class="active-call-display" *ngIf="callState?.isInCall">
  <div class="active-call-info">
    <span class="call-duration">{{ 'IN_CALL' | translate }}</span>
    <button class="btn-end-call-small" (click)="endCall()">
      <i class="fas fa-phone-slash"></i>
    </button>
  </div>

  <!-- Jitsi Call Container -->
  <div id="jitsi-iframe" class="jitsi-container"></div>
</div>
